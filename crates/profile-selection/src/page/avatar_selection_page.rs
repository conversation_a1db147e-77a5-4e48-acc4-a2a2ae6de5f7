use std::rc::Rc;

use amzn_fable_tokens::{FableSpacing, FableBorder, FableColor};
use fableous::typography::typography::{TypographyBody200, TypographyBody200Caller, TypographyBody200Props};
use fableous::utils::get_ignx_color;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::id::Id;
use ignx_compositron::layout::*;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::{SignalWith, create_namespace, create_rw_signal, Signal};
use ignx_compositron::{compose, Composer};
use mockall_double::*;
#[double]
use profile_manager::use_profile_manager;

// Import BasicCarouselUI and related types
use containers::basic_carousel::{BasicCarouselUI, BasicCarouselUICaller, BasicCarouselUIProps, HeaderContent};
use crate::ui::compound_components::profile_avatar_v2::{ProfileAvatarImageV2, ProfileAvatarImageV2Caller, ProfileAvatarImageV2Props};

/// Avatar data structure
#[derive(Clone)]
pub struct Avatar {
    pub id: String,
    pub name: String,
    pub image_url: Option<String>,
}

impl Id for Avatar {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Avatar category data structure
#[derive(Clone)]
pub struct AvatarCategory {
    pub name: String,
    pub avatars: Vec<Avatar>,
}

impl Id for AvatarCategory {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.name
    }
}

/// Create mock avatar data for a category using the current user's avatar URL
fn create_avatar_data_for_category(category_name: &str, current_user_avatar_url: &str) -> Vec<Avatar> {
    // Base avatar data for different categories
    let base_avatars = vec![
        ("1", "Avatar 1"),
        ("2", "Avatar 2"),
        ("3", "Avatar 3"),
        ("4", "Avatar 4"),
        ("5", "Avatar 5"),
        ("6", "Avatar 6"),
        ("7", "Avatar 7"),
        ("8", "Avatar 8"),
        ("9", "Avatar 9"),
        ("10", "Avatar 10"),
        ("11", "Avatar 11"),
        ("12", "Avatar 12"),
    ];

    // Use the current user's avatar URL for all avatar options
    base_avatars.into_iter().map(|(id, name)| Avatar {
        id: format!("{}_{}", category_name.to_lowercase().replace(" ", "_"), id),
        name: name.to_string(),
        image_url: Some(current_user_avatar_url.to_string()),
    }).collect()
}

/// Create mock avatar categories
fn create_avatar_categories(current_user_avatar_url: &str) -> Vec<AvatarCategory> {
    let category_names = vec![
        "The Summer I Turned Pretty",
        "The Boys",
        "Jack Ryan",
        "The Marvelous Mrs. Maisel",
        "Rings of Power",
        "Citadel",
        "Fallout",
        "Mr. & Mrs. Smith",
        "Reacher",
        "The Wheel of Time",
    ];

    category_names.into_iter().map(|name| AvatarCategory {
        name: name.to_string(),
        avatars: create_avatar_data_for_category(name, current_user_avatar_url),
    }).collect()
}

#[Composer]
pub fn AvatarSelectionPage(ctx: &AppContext) -> impl VisualComposable<'static> {
    log::info!("[avatar_selection_page] Avatar selection page start.");

    // Get the current user's profile to use their avatar URL
    let profile_manager = use_profile_manager(ctx.scope());
    let current_user_avatar_url = profile_manager
        .get_active_profile()
        .with(|profile_opt| {
            profile_opt
                .as_ref()
                .map(|profile| profile.avatar.avatarUrl.clone())
                .unwrap_or_else(|| "https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=User".to_string())
        });

    // Create dynamic avatar categories
    let avatar_categories = create_avatar_categories(&current_user_avatar_url);
    let categories_signal = create_rw_signal(ctx.scope(), avatar_categories);

    // Create a shared focus namespace for all carousels
    let global_focus_namespace = create_namespace(ctx.scope(), "avatar-selection-focus");

    // Track which carousel is currently focused (for vertical navigation)
    let focused_carousel_index = create_rw_signal(ctx.scope(), 0usize);

    // Helper function to create avatar carousel for a category
    let create_avatar_carousel = move |ctx: &AppContext, category: &AvatarCategory, category_index: usize| {
        let category_name = category.name.clone();
        let avatars = category.avatars.clone();

        // Only the first carousel gets the focus namespace for the focus ring
        let is_focused_carousel = focused_carousel_index.get() == category_index;

        let category_name_for_builder = category_name.clone();
        let avatar_builder = Rc::new(move |ctx: &AppContext, avatar: &Avatar, avatar_index: Signal<usize>| {
            let avatar_url = avatar.image_url.as_ref()
                .map(|s| s.as_str())
                .unwrap_or("https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=Avatar");

            // Create a signal for the avatar URL
            let url_signal = create_rw_signal(ctx.scope(), avatar_url.to_string());

            // Create focus signal for this avatar
            let focused_signal = create_focus_signal(ctx.scope());
            let is_focused = Signal::derive(ctx.scope(), move || focused_signal.get());

            compose! {
                ProfileAvatarImageV2(
                    url: url_signal.read_only(),
                    avatar_radius: 0.0,  // Square avatars for avatar selection
                    base_image_radius: 80.0  // Match avatar size
                )
                .width(160.0)  // Avatar width
                .height(160.0) // Avatar height
                .focusable()
                .focused(focused_signal)
                // Only the first avatar of the focused carousel gets the focus namespace
                .matched_geometry(
                    global_focus_namespace,
                    true,
                    is_focused.get() && is_focused_carousel && avatar_index.get_untracked() == 0
                )
                .test_id(format!("avatar-item-{}-{}", category_name_for_builder.to_lowercase().replace(" ", "-"), avatar_index.get_untracked()))
            }
        });

        let category_name_for_title = category_name.clone();
        let category_name_for_carousel = category_name.clone();
        let category_name_for_test_id = category_name.clone();

        compose! {
            Column() {
                // Category title
                TypographyBody200(content: category_name_for_title.as_str(), color: Color::white())
                    .test_id(format!("category-title-{}", category_name_for_title.to_lowercase().replace(" ", "-")))
                    .padding(Padding::new(0.0, 0.0, FableSpacing::SPACING150, 0.0))

                // Avatar carousel using BasicCarouselUI
                BasicCarouselUI(
                    items: avatars,
                    item_builder: avatar_builder,
                    spacing: FableSpacing::SPACING100,
                    on_end_reached: Rc::new(|| {}),
                    end_reached_buffer: 4,  // Use explicit value instead of DEFAULT_END_REACHED_BUFFER
                    header: HeaderContent::None,
                    on_back_pressed: Rc::new(|| {}),
                    default_focus_index: 0,
                    // Only the focused carousel gets the focus ring namespace
                    focus_ring_namespace: if is_focused_carousel { Some(global_focus_namespace) } else { None }
                )
                .test_id(format!("avatar-carousel-{}", category_name_for_carousel.to_lowercase().replace(" ", "-")))
            }
            .test_id(format!("avatar-category-{}", category_name_for_test_id.to_lowercase().replace(" ", "-")))
        }
    };

    compose! {
        Stack() {
            // Small square profile avatar in top left corner
            ProfileAvatarImageV2(
                url: current_user_avatar_url.clone(),
                avatar_radius: 0.0, // Square avatar (no border radius)
                base_image_radius: 20.0
            )
            .width(39.0)  // Match JS avatar dimension
            .height(39.0) // Match JS avatar dimension
            .translate_x(FableSpacing::SPACING200)  // 32px from left edge - slightly closer to edge
            .translate_y(FableSpacing::SPACING200)  // 32px from top edge - better vertical alignment
            .test_id("small-profile-avatar")

            // Main content with separated title and scrollable carousel area
            Column() {
                // Fixed title area that doesn't scroll
                Label(text: "Change your profile image")
                    .color(Color::white())
                    .test_id("page-title")
                    .padding(Padding::new(0.0, 0.0, FableSpacing::SPACING300, 0.0)) // Add bottom spacing

                // Carousel area using the same pattern as PlaybackVerticalCarousel
                ColumnList(
                    items: categories_signal.get(),
                    item_builder: Rc::new({
                        let create_avatar_carousel = create_avatar_carousel.clone();
                        move |ctx: &AppContext, category: &AvatarCategory, category_index: usize| {
                            create_avatar_carousel(ctx, category, category_index)
                        }
                    })
                )
                .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING300))
                .cross_axis_alignment(CrossAxisAlignment::Start)
                .auto_scroll(AutoScroll::Pivot(Pivot::Start, Animation::default()))
                .focus_area(FocusAreaEntryMode::Memo)
                .focus_hierarchical_container(NavigationStrategy::Vertical)
                .main_axis_direction(MainAxisDirection::Reverse)
                .overflow_behavior(OverflowBehavior::Hidden)
                .flex(1.0) // Take remaining space below the title
            }
            .padding(Padding::new(64.0, FableSpacing::SPACING300, FableSpacing::SPACING300, 96.0)) // Optimized top padding for better title positioning

            // Global focus ring that follows the focused avatar
            Rectangle()
                .matched_geometry(global_focus_namespace, false, true)
                .border_color(get_ignx_color(FableColor::PRIMARY))
                .border_width(FableBorder::FOCUSED_WIDTH)
                .border_radius(0.0) // Square focus ring to match square avatars
                .test_id("avatar-focus-ring")
        }
        .alignment(Alignment::StartTop)

    }
}